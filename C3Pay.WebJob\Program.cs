using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Models.Settings;
using C3Pay.Core.Models.Settings.Membership;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using C3Pay.Core.Services.C3Pay.Mock;
using C3Pay.Core.Services.C3Pay.UnEmpInsurance;
using C3Pay.Data;
using C3Pay.Services;
using C3Pay.Services.Handlers.MoneyTransfer.BankSearch.Handlers;
using C3Pay.Services.Helper;
using C3Pay.Services.Membership.Commands;
using C3Pay.Services.Mock;
using C3Pay.Services.UnEmpInsurnace;
using C3Pay.WebJob.Extensions;
using Edenred.Common.Core;
using Edenred.Common.Services;
using Edenred.Common.Services.Extension;
using Edenred.Common.Services.Extensions;
using Edenred.Common.Services.Integration.MockService;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Services.Membership;
using C3Pay.Services.Infobip;
using Microsoft.Azure.WebJobs;
using C3Pay.Core.Abstractions.Cache;
using C3Pay.Services.Cache;
using C3Pay.Services.RmtKyc;
using C3Pay.Services.LoginVideos.Validators;
using C3Pay.Core.Repositories;
using C3Pay.Data.Repositories;
using TelemetryConfiguration = Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration;
using C3Pay.Services.BlobService;
namespace C3Pay.WebJob
{
    /// <summary>
    /// 
    /// </summary>
    class Program
    {
        private static string _environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        static async Task Main()
        {
            var builder = new HostBuilder();

            //Configure the Webjob
            builder.ConfigureWebJobs(b =>
            {
                b.AddTimers();
                b.AddAzureStorageBlobs();
                b.AddAzureStorageQueues();
                b.AddServiceBus();
            });


            //Setup the configuration
            IConfiguration configuration = null;
            builder.ConfigureAppConfiguration(configBuilder =>
            {
                configuration = configBuilder.SetBasePath(Directory.GetCurrentDirectory())
                                                 .AddJsonFile("appsettings.json", optional: false)
                                                 .AddJsonFile($"appsettings.{_environmentName}.json", optional: true)
                                                 .AddEnvironmentVariables()
                                                 .Build();

                if (!string.IsNullOrEmpty(configuration.GetConnectionString("AzureAppConfig")))
                {
                    configBuilder.AddAzureAppConfiguration(Options => Options.Connect(configuration.GetConnectionString("AzureAppConfig")).UseFeatureFlags());
                }

                if (!string.IsNullOrWhiteSpace(configuration["KeyVault:Authority"]))
                {
                    configBuilder.AddAzureKeyVault(configuration["KeyVault:Authority"]);
                }

                configuration = configBuilder.Build();
            });

            //Setup the logging
           builder.ConfigureLogging((hostingContext, logging) =>
            {
                logging.AddConsole();

                var instrumentationKey = hostingContext.Configuration["APPINSIGHTS_INSTRUMENTATIONKEY"];

                logging.AddApplicationInsightsWebJobs(o => o.InstrumentationKey = instrumentationKey);

                // Manually configure TelemetryConfiguration
                var serviceProvider = logging.Services.BuildServiceProvider();
                var telemetryConfiguration = serviceProvider.GetRequiredService<TelemetryConfiguration>();

                telemetryConfiguration.InstrumentationKey = instrumentationKey;
                telemetryConfiguration.DefaultTelemetrySink.TelemetryProcessorChainBuilder
                    .Use(next => new RandomSamplingProcessor(next, 0.3))
                    .Build();
            });

            builder.ConfigureServices(services =>
            {
                services.AddHttpContextAccessor();

                services.InjectValidators();

                services.AddScoped<IUnitOfWork, UnitOfWork>();
                services.AddScoped<IUnitOfWorkReadOnly, UnitOfWorkReadOnly>();

                services.AddDbContext<C3PayContext>(
                  options => options.UseSqlServer(configuration.GetConnectionString("C3PayConnection"), x => x.MigrationsAssembly("C3Pay.Data")));

                services.AddDbContext<C3PayContextReadonly>(
                    options =>
                    options.UseSqlServer(configuration.GetConnectionString("C3PayConnectionReadOnly"),
                    x => x.MigrationsAssembly("C3Pay.Data"))
                );

                //Azure services
                services.AddKeyVault(configuration);
                services.AddBlobStorageService(configuration, "AzureWebJobsStorage");
                services.AddTransient<IMessagingQueueService, ServiceBusMessagingService>();
                services.AddTransient<IAuditTrailService, AuditTrailService>();
                services.AddTransient<IPartnerCorporateService, PartnerCorporateService>();

                //Add resource service
                services.AddTransient<IResourceFileService, ResourceFileService>();

                //Message sender service
                services.AddTransient<ITextMessageSenderService, TextMessageSenderService>();
                services.AddTransient<ITextMessageSenderMockService, TextMessageSenderMockService>();

                //cache
                services.AddTransient<ICacheService, CacheService>();

                //Etisalat SMS Services
                services.AddEtisalatSMSService(configuration);

                //Etisalat SMS Services
               services.AddInfobipSMSService(configuration);

                //register EdenredIdentiyManager
                services.AddEdenredIdentityManagerService(configuration);

                services.AddTransient<IUIElementRepository, UIElementRepository>();

                //Money Transfer                      
                services.AddTransient<IMoneyTransferBeneficiaryService, MoneyTransferBeneficiaryService>();
                services.AddTransient<IEHMoneyTransferService, EHMoneyTransferService>();
                services.AddTransient<IMoneyTransferService, MoneyTransferService>();
                services.AddTransient<ISftpProcessorService, SftpProcessorService>();
                services.AddTransient<ISFTPService, SFTPService>();
                services.AddTransient<IReferralProgramService, ReferralProgramService>();
                services.AddTransient<ISpendPolicyService, SpendPolicyService>();
                services.AddTransient<ISalaryAdvanceCashBackService, SalaryAdvanceCashBackService>();

                //RMT
                services.AddTransient<IAuditTrailService, AuditTrailService>();
                services.AddTransient<IRMTProfileService, RMTProfileService>();
                services.AddTransient<IPushNotificationSenderService, PushNotificationSenderService>();
                services.AddTransient<IEmailSenderService, EmailSenderService>();
                services.AddTransient<ICardHolderService, CardHolderService>();
                services.Configure<CleverTapServiceSettings>(configuration.GetSection("CleverTapService"));
                services.AddCleverTapService(configuration);
                services.AddTransient<IAnalyticsPublisherService, AnalyticsPublisherService>();
                services.AddTransient<IRmtKycRefinementService, RmtKycRefinementService>();

                services.AddTransient<IUserService, UserService>();

                //Mobile Recharge Service
                services.AddTransient<IMobileRechargeService, MobileRechargeService>();
                services.Configure<MobileRechargeServiceSettings>(configuration.GetSection("MobileRechargeService"));

                services.Configure<MoneyTransferServiceSettings>(configuration.GetSection("MoneyTransferService"));
                services.Configure<RAKSFTPConnectionSettings>(configuration.GetSection("RAKSFTPConnection"));
                services.Configure<ReplacementCardServiceSettings>(configuration.GetSection("ReplacementCardUpdateService"));
                services.Configure<ReferralProgramServiceSettings>(configuration.GetSection("ReferralProgramService"));
                services.Configure<BranchIdUpdateServiceSettings>(configuration.GetSection("BranchIdUpdateService"));
                services.Configure<KycExpirySettings>(configuration.GetSection("KycExpiry"));
                services.Configure<KycUnblockByPassportSettings>(configuration.GetSection("KycUnblockByPassport"));
                services.Configure<GeneralSettings>(configuration.GetSection("General"));

                //Add Rak Service
               if (Convert.ToBoolean(configuration["MoneyTransferService:EnableRakMock"]))
                {
                    services.AddScoped<IRAKService, RakMockService>();
                    services.Configure<RAKSettings>(configuration.GetSection("RAKService"));
                }
                else
                {
                    services.AddRakService(configuration, (_environmentName == "Development"));
                }

                services.AddRakBankMoneyTransferService(configuration);

                //Ad PPS Service
                services.AddPPSService(configuration);
                services.AddPPSWebAuthService(configuration);

                //Register Ding Service
                services.AddDingService(configuration);

                //Add KYC service
                services.AddKYCService(configuration);

                services.Configure<KycBlockExclusionsSettings>(configuration.GetSection("KycBlockExclusions"));
                services.AddTransient<IKycBlockExclusionService,  KycBlockExclusionService>();
                //Add ESMO Service
                services.AddESMOWebService(configuration);

                // Lookup
                services.AddTransient<ILookupService, LookupService>();

                // Add Exchange House Money Transfer service.
                services.AddExchangeHouseMoneyTransferService(configuration);

                //Add caching
                if (Convert.ToBoolean(configuration["General:EnableRedis"]))
                 {
                     services.AddRedisCaching(configuration, "C3Pay");
                 }
                 else
                 {
                     services.AddDistributedMemoryCache();
                 }

                 services.AddFirebaseCloudMessagingService(configuration);

                 services.AddSendGridService(configuration);

                 services.AddTransactionsB2CService(configuration);

                #region Mock Services

                services.AddTransient<ITextMessageSenderMockService, TextMessageSenderMockService>();
                services.AddTransient<IMoneyTransferBeneficiaryMockService, MoneyTransferBeneficiaryMockService>();
                services.AddTransient<IMoneyTransferMockService, MoneyTransferMockService>();
                services.AddTransient<IRAKMockService, RAKMockService>();
                services.AddEtisalatSMSMockService(configuration);
                services.AddInfobipSMSMockService(configuration);
                services.AddEdenredIdentityManagerMockService(configuration);
                services.AddPPSWebAuthMockService(configuration);

                #endregion

                //  services.AddAutoMapper(typeof(Program), typeof(Edenred.Common.Services.MappingProfile));

                services.AddAutoMapper(typeof(Program).Assembly, typeof(Edenred.Common.Services.MappingProfile).Assembly);

                // services.AddAutoMapper(cfg =>
                // {
                //     cfg.AddProfile<Edenred.Common.Services.MappingProfile>();
                //     // Add other profiles if needed
                // });
                // Bill Payment Related - Paykii Service Inject 
                services.AddPaykiiService(configuration);
                services.AddPaykiiMockService(configuration);
                services.AddTransient<IBillPaymentProcessingService, BillPaymentProcessingService>();

                services.AddScoped<SynchronizeBanksAndBranchesHandler>();
                services.AddTransient<IBillPaymentProcessingMockService, BillPaymentProcessingMockService>();
                services.AddTransient<IUserMockService, UserMockService>();
                services.Configure<BillPaymentSettings>(configuration.GetSection("BillPayment"));

                // PDF service.
                services.AddTransient<IPDFService, PDFService>();

                // UnEmployment Insurance 
                services.Configure<UnEmpInsuranceSettings>(configuration.GetSection("UnEmploymentInsurance"));
                services.AddDubaiInsuranceService(configuration);
                services.AddTransient<IUnEmpInsuranceService, UnEmpInsuranceService>();
                services.AddTransient<IUnEmpInsuranceLookupService, UnEmpInsuranceLookupService>();

                //subscription
                services.AddTransient<ISubscriptionService, SubscriptionService>();

                //audit trail
                services.AddTransient<IPortalUserService, PortalUserService>();
                services.AddTransient<IAuditTrailService, AuditTrailService>();

                // user interface
                services.AddTransient<IUserInterfaceService, UserInterfaceService>();

                // Testing Settings
                services.Configure<TestingSettings>(configuration.GetSection("Testing"));

                services.AddTransient<IKycUnblockService, KycUnblockService>();

                #region C3Pay+
                services.Configure<C3PayPlusMembershipSettings>(configuration.GetSection("C3PayPlusMembership"));
                services.AddScoped<IC3PayPlusMembershipLookupService, C3PayPlusMembershipLookupService>();
                services.AddTransient<IUserRepository, UserRepository>();
                services.AddTransient<C3PayPlusMembershipLoginVideoValidator>();
                services.AddScoped<IC3PayPlusMembershipTranslationService, C3PayPlusMembershipTranslationService>();
                services.AddScoped<ICsvConfigService, CsvConfigService>();
                #endregion

                services.AddAzureAppConfiguration();

                services.AddFeatureManagement().AddFeatureFilter<MRCrossCheckBeneficiaryFeatureFilter>();

                services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ReverseAtmWithdrawalCommand).Assembly));

                services.AddSanctionScreeningService(configuration);

                services.AddRewardService(configuration);
            });

            //Run the host
            var host = builder.Build();

            using (host)
             {
                 Log.Logger.Information("WebJob starting up...");

                 await host.RunAsync();

                 Log.Logger.Information("WebJob shutdown");
            }
        }
    }

}
