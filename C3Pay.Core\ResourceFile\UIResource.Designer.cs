﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace C3Pay.Core.ResourceFile {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class UIResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal UIResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("C3Pay.Core.ResourceFile.UIResource", typeof(UIResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to en##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_en.mp4 | bn##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_bn.mp4 | hi##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_hi.mp4 | ml##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_ml.mp4 | ta##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_ta.mp4 | te##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_te.mp4 | ur-en##https://cdn.edenred.ae/device-binding/DeviceBinding_Onlogin_ur-en.m [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string devicebindingawareness_initialvideo {
            get {
                return ResourceManager.GetString("devicebindingawareness_initialvideo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to en##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_en.mp4 | bn##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_bn.mp4 | hi##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_hi.mp4 | ml##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_ml.mp4 | ta##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_ta.mp4 | te##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_te.mp4 | ur-en##https://cdn.edenred.ae/device-binding/DeviceBinding_Oncvv_ur-en.mp4.
        /// </summary>
        internal static string devicebindingawareness_recurringvideo {
            get {
                return ResourceManager.GetString("devicebindingawareness_recurringvideo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to bn##https://cdn.edenred.ae/security-sms-awareness/securitysms_bn.mp4 | en##https://cdn.edenred.ae/security-sms-awareness/securitysms_en.mp4 | hi##https://cdn.edenred.ae/security-sms-awareness/securitysms_hi.mp4 | ml##https://cdn.edenred.ae/security-sms-awareness/securitysms_ml.mp4 | ta##https://cdn.edenred.ae/security-sms-awareness/securitysms_ta.mp4 | te##https://cdn.edenred.ae/security-sms-awareness/securitysms_te.mp4 | ur-en##https://cdn.edenred.ae/security-sms-awareness/securitysms_ur-en.mp4.
        /// </summary>
        internal static string securitysms_video {
            get {
                return ResourceManager.GetString("securitysms_video", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to bn##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_bn.mp4 | 
        ///en##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_en.mp4 | 
        ///hi##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_hi.mp4 | 
        ///ml##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_ml.mp4 | 
        ///ta##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_ta.mp4 | 
        ///te##https://cdn.edenred.ae/security-sms-migration/securitysmsmigration_te.mp4 | 
        ///ur-en##https://cdn.e [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string securitysmsmigration_video {
            get {
                return ResourceManager.GetString("securitysmsmigration_video", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Account_Details_Heading {
            get {
                return ResourceManager.GetString("t9n_Account_Details_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Account_Number_Placeholder {
            get {
                return ResourceManager.GetString("t9n_Account_Number_Placeholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_AccountNumber {
            get {
                return ResourceManager.GetString("t9n_AccountNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_AccountNumberOrIBAN {
            get {
                return ResourceManager.GetString("t9n_AccountNumberOrIBAN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Choose_Option_Heading {
            get {
                return ResourceManager.GetString("t9n_Choose_Option_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_District_Heading {
            get {
                return ResourceManager.GetString("t9n_District_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_FirstName {
            get {
                return ResourceManager.GetString("t9n_FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_FullName {
            get {
                return ResourceManager.GetString("t9n_FullName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_FullName_Wallet_Example {
            get {
                return ResourceManager.GetString("t9n_FullName_Wallet_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Help {
            get {
                return ResourceManager.GetString("t9n_Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_IBAN_Example {
            get {
                return ResourceManager.GetString("t9n_IBAN_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_IFSC {
            get {
                return ResourceManager.GetString("t9n_IFSC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_IFSC_Code_Example {
            get {
                return ResourceManager.GetString("t9n_IFSC_Code_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_IFSC_Details_Heading {
            get {
                return ResourceManager.GetString("t9n_IFSC_Details_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Last_Name_Example {
            get {
                return ResourceManager.GetString("t9n_Last_Name_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_LastName {
            get {
                return ResourceManager.GetString("t9n_LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Middle_Name_Example {
            get {
                return ResourceManager.GetString("t9n_Middle_Name_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_MiddleName {
            get {
                return ResourceManager.GetString("t9n_MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Name_Example {
            get {
                return ResourceManager.GetString("t9n_Name_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Next_Button {
            get {
                return ResourceManager.GetString("t9n_Next_Button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Personal_Details_Heading {
            get {
                return ResourceManager.GetString("t9n_Personal_Details_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_PhoneNumber {
            get {
                return ResourceManager.GetString("t9n_PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Postal_Code {
            get {
                return ResourceManager.GetString("t9n_Postal_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Postal_Code_Heading {
            get {
                return ResourceManager.GetString("t9n_Postal_Code_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_PostalCode {
            get {
                return ResourceManager.GetString("t9n_PostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Search_Bank_Branch_Example {
            get {
                return ResourceManager.GetString("t9n_Search_Bank_Branch_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Search_Bank_Example {
            get {
                return ResourceManager.GetString("t9n_Search_Bank_Example", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Select_Bank {
            get {
                return ResourceManager.GetString("t9n_Select_Bank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Select_Bank_description {
            get {
                return ResourceManager.GetString("t9n_Select_Bank_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Select_Bank_Heading {
            get {
                return ResourceManager.GetString("t9n_Select_Bank_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Select_Branch_Heading {
            get {
                return ResourceManager.GetString("t9n_Select_Branch_Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string t9n_Select_IFSC_Code {
            get {
                return ResourceManager.GetString("t9n_Select_IFSC_Code", resourceCulture);
            }
        }
    }
}
